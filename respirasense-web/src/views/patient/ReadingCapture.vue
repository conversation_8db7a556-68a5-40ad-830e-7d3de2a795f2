<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="reading-capture-container">
        <ReadingCapture />
      </div>
    </main>
    <AppFooter />
  </div>
</template>

<script>
import AppHeader from '@/components/layout/Header.vue';
import AppFooter from '@/components/layout/Footer.vue';
import ReadingCapture from '@/components/patient/ReadingCapture.vue';

export default {
  name: 'PatientReadingCaptureView',

  components: {
    AppHeader,
    AppFooter,
    ReadingCapture
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/components/patient/ReadingCapture.scss';

.default-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.main-content {
  background: transparent;
  padding: 2rem 0;
}

.reading-capture-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .reading-capture-container {
    padding: 1rem;
  }

  .main-content {
    padding: 1rem 0;
  }
}
</style>
