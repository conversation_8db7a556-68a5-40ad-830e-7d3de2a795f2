<template>
  <div class="default-layout">
    <AppHeader />
    <main class="main-content">
      <div class="patient-dashboard">
        <div class="dashboard-header">
          <div class="header-main">
            <div class="section-title">
              <i class="fas fa-heartbeat"></i>
              <h1>Health Dashboard</h1>
            </div>
            <div class="last-reading" v-if="lastReading && lastReading.timestamp">
              <i class="fas fa-clock"></i>
              Last reading: {{ formatDate(lastReading.timestamp) }}
            </div>
          </div>

          <!-- Action Buttons (Moved to top) -->
          <div class="action-buttons header-actions">
            <button @click="viewDetailedReport" class="secondary-button">
              <i class="fas fa-file-medical"></i>
              View Detailed Report
            </button>
            <button @click="downloadHistory" class="secondary-button" :disabled="isDownloading">
              <i class="fas fa-download"></i>
              {{ isDownloading ? 'Downloading...' : 'Download History' }}
            </button>
            <button @click="navigateToPractitioners" class="secondary-button">
              <i class="fas fa-user-md"></i>
              My Practitioners
              <span v-if="pendingRequests && pendingRequests.length > 0" class="notification-badge">
                {{ pendingRequests.length }}
              </span>
            </button>
          </div>
        </div>

        <!-- COPD Risk Status Card -->
        <div class="status-card" :class="copdRiskLevel">
          <div class="status-icon">
            <i :class="getStatusIcon"></i>
          </div>
          <div class="status-content">
            <h2>COPD Risk Status</h2>
            <p class="status-level">{{ (copdRiskLevel || 'normal').toUpperCase() }}</p>
            <p class="status-message">{{ getStatusMessage }}</p>
          </div>
          <button @click="requestNewReading" :disabled="isReadingInProgress" class="action-button">
            {{ isReadingInProgress ? 'Reading in Progress...' : 'Take New Reading' }}
          </button>
        </div>

        <!-- Key Metrics Section -->
        <div class="metrics-heading">
          <div class="section-title">
            <i class="fas fa-chart-bar"></i>
            <h2>Key Health Metrics</h2>
          </div>
        </div>
        <div class="metrics-grid">
          <div class="metric-card" v-for="metric in metrics" :key="metric.key">
            <div class="metric-header">
              <i :class="getMetricIcon(metric.key)"></i>
              <h3>{{ metric.label }}</h3>
            </div>

            <!-- Respiratory Rate tile with spirometry data -->
            <template v-if="metric.key === 'respiratoryRate' && latestMetrics.spirometry">
              <div class="spirometry-enhanced-rr">
                <div class="primary-metric">
                  <div class="metric-value">{{ formattedMetrics[metric.key] || '--' }}</div>
                  <div class="metric-unit">{{ metric.unit }}</div>
                  <div class="calculation-note">Calculated from spirometry</div>
                </div>
                <div class="spirometry-details">
                  <div class="spirometry-row">
                    <span class="label">FEV₁:</span>
                    <span class="value">{{ latestMetrics.spirometry.FEV1?.toFixed(2) || '--' }}L</span>
                  </div>
                  <div class="spirometry-row">
                    <span class="label">FVC:</span>
                    <span class="value">{{ latestMetrics.spirometry.FVC?.toFixed(2) || '--' }}L</span>
                  </div>
                  <div class="spirometry-row">
                    <span class="label">Ratio:</span>
                    <span class="value">{{ latestMetrics.spirometry.FEV1_FVC ? (latestMetrics.spirometry.FEV1_FVC * 100).toFixed(1) : '--' }}%</span>
                  </div>
                  <div class="spirometry-row">
                    <span class="label">% Predicted:</span>
                    <span class="value">{{ latestMetrics.spirometry.percent_predicted || '--' }}%</span>
                  </div>
                </div>
              </div>
            </template>

            <!-- Standard metric display -->
            <template v-else>
              <div class="metric-value">{{ formattedMetrics[metric.key] || '--' }}</div>
              <div class="metric-unit">{{ metric.unit }}</div>
            </template>

            <ChartJSTrendChart
              :data="getHistoryData(metric.key)"
              :height="80"
            />
          </div>
        </div>

        <!-- Recent Readings Section -->
        <div class="readings-section">
          <div class="readings-header">
            <div class="section-title">
              <i class="fas fa-chart-line"></i>
              <h2>Recent Readings</h2>
            </div>
            <div class="view-toggle">
              <button
                @click="toggleViewMode('calendar')"
                :class="{ active: viewMode === 'calendar' }"
                class="toggle-btn"
              >
                <i class="fas fa-calendar-alt"></i> Calendar
              </button>
              <button
                @click="toggleViewMode('list')"
                :class="{ active: viewMode === 'list' }"
                class="toggle-btn"
              >
                <i class="fas fa-list"></i> List
              </button>
            </div>
          </div>

          <!-- Views Container -->
          <div class="views-container">
            <!-- Calendar View -->
            <div v-show="viewMode === 'calendar'" class="calendar-view">
              <!-- The calendar now loads its own data independently -->
              <readings-calendar ref="calendarComponent" />
            </div>

            <!-- List View -->
            <div v-show="viewMode === 'list'" class="readings-timeline">
              <div class="timeline-container" ref="timelineContainer">
                <!-- Make sure recentReadings is defined and is an array before iterating -->
                <div v-if="recentReadings && recentReadings.length > 0">
                  <div v-for="(reading, index) in recentReadings"
                      :key="reading.id || index"
                      class="timeline-item"
                      :class="[reading.riskLevel || 'normal', { 'last-item': index === (recentReadings ? recentReadings.length - 1 : 0) }]">
                    <div class="timeline-date">
                      <!-- Handle different timestamp formats -->
                      <template v-if="reading.timestamp && typeof reading.timestamp === 'object' && reading.timestamp.seconds">
                        {{ formatDate(new Date(reading.timestamp.seconds * 1000)) }}
                      </template>
                      <template v-else-if="reading.timestamp && typeof reading.timestamp === 'object' && reading.timestamp._seconds">
                        {{ formatDate(new Date(reading.timestamp._seconds * 1000)) }}
                      </template>
                      <template v-else>
                        {{ formatDate(reading.timestamp) }}
                      </template>
                    </div>
                    <div class="timeline-content">
                      <h4>COPD Risk Level: {{ (reading.riskLevel || 'normal').toUpperCase() }}</h4>
                      <div class="reading-metrics">
                        <!-- Show calculated respiratory rate from spirometry if available -->
                        <template v-if="reading.spirometry">
                          <span class="calculated-rr">RR: {{ calculateRespiratoryRateFromSpirometry(reading.spirometry) || '--' }} bpm (calc)</span>
                          <span>FEV₁: {{ reading.spirometry.FEV1?.toFixed(2) || '--' }}L</span>
                          <span>Ratio: {{ reading.spirometry.FEV1_FVC ? (reading.spirometry.FEV1_FVC * 100).toFixed(1) : '--' }}%</span>
                        </template>
                        <template v-else>
                          <span>RR: {{ formatDecimal(reading.respiratoryRate) }} bpm</span>
                        </template>
                        <span>O₂: {{ formatDecimal(reading.oxygenSaturation) }}%</span>
                        <span>HR: {{ formatDecimal(reading.heartRate) }} bpm</span>
                        <span>Temp: {{ formatDecimal(reading.temperature) }}°C</span>
                      </div>
                      <p v-if="reading.notes" class="reading-notes">{{ reading.notes }}</p>
                    </div>
                  </div>
                </div>

                <!-- Loading indicator -->
                <div v-if="loadingMore" class="loading-indicator">
                  <div class="spinner"></div>
                  <span>Loading more readings...</span>
                </div>

                <!-- Load more button -->
                <div v-if="hasMoreReadings && !loadingMore && recentReadings && recentReadings.length > 0" class="load-more-container">
                  <button @click="loadMoreReadings" class="load-more-btn">
                    Load More
                  </button>
                </div>

                <!-- No more readings message -->
                <div v-if="!hasMoreReadings && recentReadings && recentReadings.length > 0" class="no-more-readings">
                  No more readings to load
                </div>

                <!-- No readings message -->
                <div v-if="!loadingMore && (!recentReadings || recentReadings.length === 0)" class="no-readings">
                  No readings available
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </main>
    <AppFooter />
  </div>
</template>

<script>
import ChartJSTrendChart from '@/components/charts/ChartJSTrendChart.vue';
import AppHeader from '@/components/layout/Header.vue';
import AppFooter from '@/components/layout/Footer.vue';
import ReadingsCalendar from '@/components/patient/ReadingsCalendar.vue';
import { useDashboard } from './composables/useDashboard';
import { useStore } from 'vuex';
import { computed, onMounted } from 'vue';

export default {
  name: 'PatientDashboard',

  components: {
    ChartJSTrendChart,
    AppHeader,
    AppFooter,
    ReadingsCalendar
  },

  setup() {
    const dashboard = useDashboard();
    const store = useStore();

    // Get pending practitioner requests from store
    const pendingRequests = computed(() => {
      return store.state.patient?.pendingRequests || [];
    });

    // Fetch pending requests on component mount
    onMounted(async () => {
      await store.dispatch('patient/fetch_pending_requests');
    });

    // Add debugging to check if setViewMode is available
    console.log('Dashboard setup - Available methods:', Object.keys(dashboard));
    console.log('Dashboard setup - setViewMode type:', typeof dashboard.setViewMode);
    console.log('Dashboard setup - viewMode value:', dashboard.viewMode.value);

    // Create a simplified toggle function
    const toggleViewMode = (mode) => {
      console.log(`Dashboard - toggleViewMode called with mode: ${mode}`);
      console.log(`Dashboard - Current viewMode: ${dashboard.viewMode.value}`);

      // Only change if it's different from current mode
      if (dashboard.viewMode.value !== mode) {
        try {
          // Use the setViewMode function from the readings composable
          dashboard.setViewMode(mode);

          // Force immediate loading of data for the list view if needed
          if (mode === 'list' && (!dashboard.recentReadings.value || dashboard.recentReadings.value.length === 0)) {
            console.log('List view selected but no readings loaded, initializing readings');
            dashboard.initializeReadings();
          }

          // Verify the change
          console.log(`Dashboard - After change, viewMode is now: ${dashboard.viewMode.value}`);
        } catch (error) {
          console.error('Error toggling view mode:', error);
        }
      }
    };

    // Add the toggleViewMode function to the dashboard object
    dashboard.toggleViewMode = toggleViewMode;

    // Add pendingRequests to the returned object
    return {
      ...dashboard,
      pendingRequests,
      // Make sure calculateRespiratoryRateFromSpirometry is available in template
      calculateRespiratoryRateFromSpirometry: dashboard.calculateRespiratoryRateFromSpirometry
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/views/patient/Dashboard.scss';

.notification-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  font-size: 0.7rem;
  font-weight: bold;
  padding: 0 4px;
  margin-left: 6px;
  position: relative;
  top: -1px;
}

.secondary-button {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.views-container {
  width: 100%;
  position: relative;
}

.calendar-view {
  width: 100%;
}
</style>
