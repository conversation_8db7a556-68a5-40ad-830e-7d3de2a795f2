import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import { format } from 'date-fns';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { useFormatters } from '@/composables/useFormatters';

/**
 * Composable for readings functionality
 * @returns {Object} Readings utility functions and state
 */
export function useReadings() {
  const store = useStore();
  const { formatDate, formatDecimal, formatDateTime } = useFormatters();

  // State
  const loadingMore = ref(false);
  const recentReadings = ref([]);
  const hasMoreReadings = ref(true);
  const readingsPerPage = ref(5);
  const isDownloading = ref(false);
  const viewMode = ref('calendar');
  const error = ref(null);

  /**
   * Load more readings
   */
  const loadMoreReadings = async () => {
    if (loadingMore.value || !hasMoreReadings.value) return;

    try {
      loadingMore.value = true;

      // Ensure recentReadings is initialized as an array
      if (!recentReadings.value || !Array.isArray(recentReadings.value)) {
        console.log('loadMoreReadings - recentReadings is not initialized, initializing as empty array');
        recentReadings.value = [];
      }

      // Get the last reading if available
      const lastReading = recentReadings.value.length > 0 ?
        recentReadings.value[recentReadings.value.length - 1] : null;
      const lastTimestamp = lastReading ? (lastReading.timestamp || lastReading.lastUpdated) : null;

      const moreReadings = await store.dispatch('patient/health/fetchMoreReadings', {
        limit: readingsPerPage.value,
        lastTimestamp
      });

      // Make sure moreReadings is an array before using it
      if (moreReadings && Array.isArray(moreReadings)) {
        if (moreReadings.length < readingsPerPage.value) {
          hasMoreReadings.value = false;
        }

        if (moreReadings.length > 0) {
          recentReadings.value = [...recentReadings.value, ...moreReadings];
        }
      } else {
        console.warn('fetchMoreReadings did not return an array:', moreReadings);
        hasMoreReadings.value = false;
      }
    } catch (err) {
      error.value = 'Failed to load more readings';
      console.error('Error loading more readings:', err);
      hasMoreReadings.value = false;
    } finally {
      loadingMore.value = false;
    }
  };

  /**
   * Initialize readings
   */
  const initializeReadings = async () => {
    try {
      // Ensure recentReadings is initialized as an array
      if (!recentReadings.value || !Array.isArray(recentReadings.value)) {
        recentReadings.value = [];
      }

      const initialReadings = await store.dispatch('patient/health/fetchMoreReadings', {
        limit: readingsPerPage.value
      });

      // Debug: Log the initial readings
      console.log('Initial readings loaded:', initialReadings);

      // Make sure initialReadings is an array before assigning
      if (initialReadings && Array.isArray(initialReadings)) {
        recentReadings.value = initialReadings;
        hasMoreReadings.value = initialReadings.length === readingsPerPage.value;
      } else {
        console.warn('fetchMoreReadings did not return an array:', initialReadings);
        recentReadings.value = [];
        hasMoreReadings.value = false;
      }
    } catch (err) {
      error.value = 'Failed to load initial readings';
      console.error('Error loading initial readings:', err);
      // Ensure recentReadings is an array even if there's an error
      recentReadings.value = [];
      hasMoreReadings.value = false;
    }
  };

  /**
   * Set the view mode (calendar or list)
   * @param {string} mode - The view mode to set ('calendar' or 'list')
   */
  const setViewMode = (mode) => {
    if (mode !== 'calendar' && mode !== 'list') {
      console.error(`Invalid view mode: ${mode}. Must be 'calendar' or 'list'`);
      return;
    }

    console.log(`useReadings - Switching view mode from ${viewMode.value} to ${mode}`);

    // Ensure recentReadings is initialized as an array
    if (!recentReadings.value || !Array.isArray(recentReadings.value)) {
      console.log('useReadings - recentReadings is not initialized, initializing as empty array');
      recentReadings.value = [];
    }

    // Set the view mode
    viewMode.value = mode;

    // If switching to list view, make sure we have data
    if (mode === 'list' && recentReadings.value.length === 0) {
      console.log('useReadings - Switching to list view with no readings, will initialize readings');
      // We'll let the watcher handle this to avoid async issues in the setter
    }

    // Verify the change
    console.log(`useReadings - After change, viewMode is now: ${viewMode.value}`);
  };

  /**
   * Download health history as PDF
   */
  const downloadHistory = async (lastReading, copdRiskLevel) => {
    try {
      isDownloading.value = true;
      const response = await store.dispatch('patient/health/downloadHealthHistory');

      if (!response || !response.readings || !response.readings.length) {
        throw new Error('No readings data available for download');
      }

      // Generate PDF report
      generatePDF(response.readings, lastReading, copdRiskLevel);
    } catch (error) {
      console.error('Error downloading history:', error);
      // You might want to show this error to the user
      error.value = 'Failed to download health history';
    } finally {
      isDownloading.value = false;
    }
  };

  /**
   * Generate PDF report
   * @param {Array} readings - The readings data
   * @param {Object} lastReading - The last reading
   * @param {string} copdRiskLevel - The COPD risk level
   */
  const generatePDF = (readings, lastReading, copdRiskLevel) => {
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(20);
    doc.text('Health History Report', 105, 15, { align: 'center' });

    // Add date
    doc.setFontSize(12);
    try {
      doc.text(`Generated on: ${format(new Date(), 'MMMM d, yyyy')}`, 105, 25, { align: 'center' });
    } catch (error) {
      console.error('Error formatting generation date:', error);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 105, 25, { align: 'center' });
    }

    // Add patient info if available
    if (lastReading && lastReading.patientName) {
      doc.text(`Patient: ${lastReading.patientName}`, 14, 35);
    }

    // Add summary statistics
    doc.setFontSize(16);
    doc.text('Summary Statistics', 14, 45);

    doc.setFontSize(12);
    doc.text(`Total Readings: ${readings.length}`, 20, 55);
    try {
      doc.text(`Latest Reading: ${formatDate(lastReading?.timestamp || lastReading?.lastUpdated || new Date())}`, 20, 65);
    } catch (error) {
      console.error('Error formatting latest reading date:', error);
      doc.text(`Latest Reading: ${new Date().toLocaleDateString()}`, 20, 65);
    }
    doc.text(`COPD Risk Level: ${copdRiskLevel.toUpperCase()}`, 20, 75);

    // Add readings table
    doc.setFontSize(16);
    doc.text('Readings Data', 14, 90);

    const tableData = readings.map(reading => {
      try {
        return [
          formatDate(reading.timestamp || reading.lastUpdated),
          formatDecimal(reading.respiratoryRate) + ' bpm',
          formatDecimal(reading.oxygenSaturation) + '%',
          formatDecimal(reading.heartRate) + ' bpm',
          formatDecimal(reading.temperature) + '\u00b0C',
          (reading.riskLevel || 'normal').toUpperCase()
        ];
      } catch (error) {
        console.error('Error formatting reading data:', error);
        return [
          'Invalid date',
          formatDecimal(reading.respiratoryRate) + ' bpm',
          formatDecimal(reading.oxygenSaturation) + '%',
          formatDecimal(reading.heartRate) + ' bpm',
          formatDecimal(reading.temperature) + '\u00b0C',
          (reading.riskLevel || 'normal').toUpperCase()
        ];
      }
    });

    // Check if we have spirometry data to determine table headers
    const hasSpirometry = readings.some(reading => reading.spirometry);

    const headers = hasSpirometry
      ? [['Date & Time', 'FEV₁ (L)', 'FVC (L)', 'FEV₁/FVC (%)', 'Oxygen Sat.', 'Heart Rate', 'Temperature', 'COPD Risk']]
      : [['Date & Time', 'Respiratory Rate', 'Oxygen Saturation', 'Heart Rate', 'Temperature', 'COPD Risk']];

    // Update table data to include spirometry if available
    const enhancedTableData = readings.map(reading => {
      const baseData = [
        format(reading.timestamp, 'MMM d, yyyy HH:mm'),
        reading.oxygenSaturation ? `${reading.oxygenSaturation.toFixed(1)}%` : '--',
        reading.heartRate ? `${reading.heartRate.toFixed(0)} bpm` : '--',
        reading.temperature ? `${reading.temperature.toFixed(1)}°C` : '--',
        (reading.riskLevel || 'normal').toUpperCase()
      ];

      if (hasSpirometry && reading.spirometry) {
        return [
          format(reading.timestamp, 'MMM d, yyyy HH:mm'),
          reading.spirometry.FEV1 ? `${reading.spirometry.FEV1.toFixed(2)}` : '--',
          reading.spirometry.FVC ? `${reading.spirometry.FVC.toFixed(2)}` : '--',
          reading.spirometry.FEV1_FVC ? `${(reading.spirometry.FEV1_FVC * 100).toFixed(1)}` : '--',
          ...baseData.slice(1)
        ];
      } else if (!hasSpirometry) {
        return [
          format(reading.timestamp, 'MMM d, yyyy HH:mm'),
          reading.respiratoryRate ? `${reading.respiratoryRate.toFixed(0)} bpm` : '--',
          ...baseData.slice(1)
        ];
      } else {
        // Has spirometry but this reading doesn't - show empty spirometry columns
        return [
          format(reading.timestamp, 'MMM d, yyyy HH:mm'),
          '--', '--', '--',
          ...baseData.slice(1)
        ];
      }
    });

    doc.autoTable({
      startY: 95,
      head: headers,
      body: enhancedTableData,
      theme: 'striped',
      headStyles: { fillColor: [183, 21, 64] },
      styles: { fontSize: 9 },
      columnStyles: hasSpirometry ? {
        0: { cellWidth: 35 },
        1: { cellWidth: 20 },
        2: { cellWidth: 20 },
        3: { cellWidth: 25 },
        4: { cellWidth: 25 },
        5: { cellWidth: 25 },
        6: { cellWidth: 25 },
        7: { cellWidth: 25 }
      } : undefined
    });

    // Save the PDF
    try {
      doc.save(`health_history_${format(new Date(), 'yyyy-MM-dd')}.pdf`);
    } catch (error) {
      console.error('Error formatting PDF filename:', error);
      doc.save(`health_history_${new Date().toISOString().split('T')[0]}.pdf`);
    }
  };

  return {
    loadingMore,
    recentReadings,
    hasMoreReadings,
    readingsPerPage,
    isDownloading,
    viewMode,
    error,
    loadMoreReadings,
    initializeReadings,
    downloadHistory,
    setViewMode
  };
}
