<template>
  <div class="detailed-report">
    <div class="back-navigation">
      <button @click="goBack" class="back-btn">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
      </button>
    </div>

    <div class="report-header">
      <h1>Detailed Health Report</h1>
      <button @click="downloadPdf" class="download-btn" :disabled="loading || !report?.statistics">
        <i class="fas fa-download"></i> Download PDF
      </button>
    </div>

    <div class="filters-section">
      <div class="date-range-picker">
        <input type="date" v-model="fromDate" @change="fetchReport">
        <span>to</span>
        <input type="date" v-model="toDate" @change="fetchReport">
      </div>

      <div class="filter-controls">
        <div class="filter-group">
          <label>COPD Status:</label>
          <select v-model="filters.copdStatus" @change="applyFilters">
            <option value="all">All</option>
            <option value="normal">Normal</option>
            <option value="warning">Warning</option>
            <option value="danger">Danger</option>
          </select>
        </div>

        <div class="filter-group">
          <label>Reading Threshold:</label>
          <select v-model="filters.threshold" @change="applyFilters">
            <option value="all">All Readings</option>
            <option value="respiratory">Respiratory Rate</option>
            <option value="oxygen">Oxygen Saturation</option>
            <option value="heart">Heart Rate</option>
            <option value="temperature">Temperature</option>
          </select>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading report...</span>
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else-if="report?.statistics" class="report-content">
      <!-- Statistics Summary -->
      <div class="statistics-summary">
        <h2>Summary Statistics</h2>
        <div class="stats-grid">
          <div v-for="(value, metric) in report.statistics.averages"
               :key="metric"
               class="stat-card">
            <h3>Average {{ formatMetricName(metric) }}</h3>
            <p>{{ formatMetricValue(metric, value) }}</p>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <h2>Health Metrics Trends</h2>

        <!-- Show spirometry chart if data is available, otherwise show respiratory rate -->
        <div class="chart-container" v-if="hasSpirometryData">
          <ChartJSLineChart
            :key="'spirometry-chart-' + fromDate + '-' + toDate"
            :series="getChartData('spirometry')"
            :loading="loading"
            title="Spirometry (FEV₁)"
            yAxisLabel="Forced Expiratory Volume (L)"
            metricType="spirometry"
            :thresholds="{ lower: 2.0 }"
          />
        </div>

        <div class="chart-container" v-else>
          <ChartJSLineChart
            :key="'rr-chart-' + fromDate + '-' + toDate"
            :series="getChartData('respiratoryRate')"
            :loading="loading"
            title="Respiratory Rate"
            yAxisLabel="Breaths per minute"
            metricType="respiratoryRate"
            :thresholds="{ upper: 20, lower: 12 }"
          />
        </div>

        <div class="chart-container">
          <ChartJSLineChart
            :key="'o2-chart-' + fromDate + '-' + toDate"
            :series="getChartData('oxygenSaturation')"
            :loading="loading"
            title="Oxygen Saturation"
            yAxisLabel="SpO₂ (%)"
            metricType="oxygenSaturation"
            :thresholds="{ lower: 95 }"
          />
        </div>

        <div class="chart-container">
          <ChartJSLineChart
            :key="'hr-chart-' + fromDate + '-' + toDate"
            :series="getChartData('heartRate')"
            :loading="loading"
            title="Heart Rate"
            yAxisLabel="Beats per minute"
            metricType="heartRate"
            :thresholds="{ upper: 100, lower: 60 }"
          />
        </div>

        <div class="chart-container">
          <ChartJSLineChart
            :key="'temp-chart-' + fromDate + '-' + toDate"
            :series="getChartData('temperature')"
            :loading="loading"
            title="Temperature"
            yAxisLabel="Temperature (°C)"
            metricType="temperature"
            :thresholds="{ upper: 37.5 }"
          />
        </div>

        <!-- COPD Risk Classification Breakdown -->
        <div class="chart-container">
          <ChartJSPieChart
            :key="'risk-chart-' + fromDate + '-' + toDate"
            :data="getRiskDistribution()"
            :loading="loading"
            title="Risk Level Distribution"
          />
        </div>
      </div>

      <!-- Trends -->
      <div class="trends-section">
        <h2>Trends Analysis</h2>
        <div v-for="(trend, metric) in report.statistics.trends"
             :key="metric"
             class="trend-item">
          <h3>{{ formatMetricName(metric) }}</h3>
          <p>{{ trend.description }}</p>
        </div>
      </div>

      <!-- Anomalies -->
      <div class="anomalies-section">
        <h2>Notable Events</h2>
        <div v-for="(anomaly, index) in report.statistics.anomalies"
             :key="index"
             class="anomaly-item">
          <p>{{ formatDate(anomaly.date) }}: {{ anomaly.description }}</p>
        </div>
      </div>

      <!-- Readings Table -->
      <div class="readings-table-section">
        <h2>Readings Data</h2>
        <table class="readings-table">
          <thead>
            <tr v-if="hasSpirometryData">
              <th>Date & Time</th>
              <th>FEV₁ (L)</th>
              <th>FVC (L)</th>
              <th>FEV₁/FVC (%)</th>
              <th>Oxygen Saturation</th>
              <th>Heart Rate</th>
              <th>Temperature</th>
              <th>COPD Risk</th>
            </tr>
            <tr v-else>
              <th>Date & Time</th>
              <th>Respiratory Rate</th>
              <th>Oxygen Saturation</th>
              <th>Heart Rate</th>
              <th>Temperature</th>
              <th>COPD Risk</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="reading in filteredReadings" :key="reading.id" :class="reading.riskLevel">
              <td>{{ formatDateTime(reading.timestamp) }}</td>

              <!-- Spirometry data columns -->
              <template v-if="hasSpirometryData && reading.spirometry">
                <td>{{ reading.spirometry.FEV1?.toFixed(2) || '--' }}</td>
                <td>{{ reading.spirometry.FVC?.toFixed(2) || '--' }}</td>
                <td>{{ reading.spirometry.FEV1_FVC ? (reading.spirometry.FEV1_FVC * 100).toFixed(1) : '--' }}</td>
              </template>
              <template v-else-if="hasSpirometryData">
                <td>--</td>
                <td>--</td>
                <td>--</td>
              </template>
              <template v-else>
                <td>{{ formatDecimal(reading.respiratoryRate) }} bpm</td>
              </template>

              <td>{{ formatDecimal(reading.oxygenSaturation) }}%</td>
              <td>{{ formatDecimal(reading.heartRate) }} bpm</td>
              <td>{{ formatDecimal(reading.temperature) }}°C</td>
              <td class="risk-cell">{{ reading.riskLevel.toUpperCase() }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import ChartJSLineChart from '@/components/charts/ChartJSLineChart.vue';
import ChartJSPieChart from '@/components/charts/ChartJSPieChart.vue';
import { useDetailedReport } from './composables/useDetailedReport';

export default {
  name: 'DetailedReport',

  components: {
    ChartJSLineChart,
    ChartJSPieChart
  },

  setup() {
    return useDetailedReport();
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/views/patient/DetailedReport.scss';
</style>
