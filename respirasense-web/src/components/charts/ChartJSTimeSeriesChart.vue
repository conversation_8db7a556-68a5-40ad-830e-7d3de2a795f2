<template>
  <div class="time-series-chart-container">
    <h3 v-if="title" class="chart-title">{{ title }}</h3>

    <!-- Loading state -->
    <div v-if="loading || (props.series && typeof props.series.then === 'function')" class="chart-loading">
      <div class="spinner"></div>
      <span>Loading chart data...</span>
    </div>

    <!-- No data state -->
    <div v-else-if="!hasData" class="no-data-message">
      <div class="no-data-icon">
        <i class="fas fa-chart-line"></i>
        <i class="fas fa-slash overlay-icon"></i>
      </div>
      <p>No data available for this time period</p>
      <p class="no-data-hint">Try selecting a different time range or check back later</p>
    </div>

    <!-- Chart display -->
    <div v-else class="chart-wrapper">
      <canvas :id="chartId" :height="height"></canvas>
    </div>

    <!-- Error message if needed -->
    <div v-if="chartError" class="chart-error">
      <p>{{ chartError }}</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  CategoryScale,
  TimeScale
} from 'chart.js';
import 'chartjs-adapter-date-fns';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  CategoryScale,
  TimeScale
);

export default {
  name: 'ChartJSTimeSeriesChart',
  props: {
    series: {
      type: [Array, Promise],
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 300
    },
    thresholds: {
      type: Object,
      default: () => ({})
    },
    yAxisLabel: {
      type: String,
      default: ''
    },
    metricType: {
      type: String,
      default: ''
    },
    timeRange: {
      type: String,
      default: '6m' // Default to 6 months
    }
  },
  setup(props) {
    const timeRangeOptions = ref([
      { value: '1m', label: '1 Month' },
      { value: '3m', label: '3 Months' },
      { value: '6m', label: '6 Months' },
      { value: '1y', label: 'Year' }
    ]);

    // Generate a unique ID for this chart instance
    const chartId = `chart-${props.metricType}-${Math.random().toString(36).substring(2, 9)}`;

    const selectedTimeRange = ref(props.timeRange);
    const chartInstance = ref(null);
    const chartError = ref(null);

    // Create a debounced render function to prevent rapid chart recreation
    const debouncedRender = (() => {
      let timeoutId = null;
      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
          nextTick(() => {
            if (document.getElementById(chartId)) {
              renderChart();
            } else {
              console.warn(`Canvas element with ID ${chartId} not found after time range change`);
            }
          });
          timeoutId = null;
        }, 150); // 150ms debounce time
      };
    })();

    // Watch for changes in the timeRange prop
    watch(() => props.timeRange, (newTimeRange) => {
      try {
        console.log(`Time range changed from ${selectedTimeRange.value} to ${newTimeRange}`);
        selectedTimeRange.value = newTimeRange;

        // Force chart to update by recreating the chart instance
        // Make sure to destroy the chart first
        destroyChart();

        // Use the debounced render function to prevent rapid chart recreation
        debouncedRender();
      } catch (error) {
        console.error('Error in timeRange watcher:', error);
      }
    });

    // Function to safely destroy the chart
    const destroyChart = () => {
      try {
        if (chartInstance.value) {
          console.log(`Destroying chart instance for ${chartId}`);

          // Check if the chart instance has a destroy method before calling it
          if (typeof chartInstance.value.destroy === 'function') {
            chartInstance.value.destroy();
          } else {
            console.warn('Chart instance does not have a destroy method');
          }

          // Always set to null after attempting to destroy
          chartInstance.value = null;
        }
      } catch (error) {
        console.error('Error destroying chart:', error);
        // Ensure the instance is set to null even if destroy fails
        chartInstance.value = null;
      }
    };

    // Track resolved series data
    const resolvedSeries = ref(null);

    // Watch for changes in the series prop, especially if it's a Promise
    watch(() => props.series, async (newSeries) => {
      try {
        // If the series is a Promise, resolve it
        if (newSeries && typeof newSeries.then === 'function') {
          console.log(`Series is a Promise for chart ${chartId}, resolving...`);
          try {
            resolvedSeries.value = await newSeries;
            console.log(`Promise resolved for chart ${chartId}, data:`, resolvedSeries.value);

            // Render the chart with the resolved data
            // Use a small delay to ensure the DOM is updated
            setTimeout(() => {
              nextTick(() => {
                renderChart();
              });
            }, 100);
          } catch (promiseError) {
            console.error(`Error resolving Promise for chart ${chartId}:`, promiseError);
            // Set the error message
            chartError.value = `Error loading chart data: ${promiseError.message || 'Unknown error'}`;
            // Set a fallback empty series
            resolvedSeries.value = [{
              name: 'No Data',
              data: [
                { x: new Date(Date.now() - 86400000), y: 0 },
                { x: new Date(), y: 0 }
              ]
            }];
          }
        } else {
          // Otherwise, just use the value directly
          console.log(`Direct series data for chart ${chartId}:`, newSeries);
          resolvedSeries.value = newSeries;

          // Render the chart with the new data
          // Use a small delay to ensure the DOM is updated
          setTimeout(() => {
            nextTick(() => {
              renderChart();
            });
          }, 100);
        }
      } catch (error) {
        console.error(`Error handling series data for chart ${chartId}:`, error);
        resolvedSeries.value = null;
      }
    }, { immediate: true });

    const hasData = computed(() => {
      // Use the resolved series if available, otherwise use the props.series
      const series = resolvedSeries.value || props.series;

      // If series is a Promise, we don't have data yet
      if (series && typeof series.then === 'function') {
        console.log(`Series for chart ${chartId} is still a Promise, waiting for resolution`);
        return false;
      }

      // Check if we have valid series data
      const isValid = series &&
             Array.isArray(series) &&
             series.length > 0 &&
             series[0]?.data &&
             Array.isArray(series[0].data) &&
             series[0].data.length > 0;

      if (!isValid) {
        console.log(`No valid data for chart ${chartId}:`, series);
      }

      return isValid;
    });

    // Get the date range based on the selected time range
    const getDateRange = () => {
      const now = new Date();
      const endDate = now;
      let startDate;

      switch (selectedTimeRange.value) {
        case '1m':
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - 1);
          break;
        case '3m':
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - 3);
          break;
        case '1y':
          startDate = new Date(now);
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        case '6m':
        default:
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - 6);
      }

      return { startDate, endDate };
    };

    const chartData = computed(() => {
      if (!hasData.value) {
        return {
          labels: [],
          datasets: []
        };
      }

      // Get the date range
      const { startDate, endDate } = getDateRange();

      // Use the resolved series if available, otherwise use the props.series
      const seriesData = resolvedSeries.value || props.series;

      // Get the data from the series
      const datasets = seriesData.map(serie => {
        // Special handling for reading presence data
        if (props.metricType === 'readingPresence') {
          return {
            label: serie.name,
            data: serie.data.map(point => ({
              x: new Date(point.x),
              y: point.y
            })),
            borderColor: '#B71540',
            backgroundColor: 'rgba(183, 21, 64, 0.1)',
            borderWidth: 2,
            pointBackgroundColor: (context) => {
              // Green for readings taken, gray for no readings
              const value = context.raw.y;
              return value === 1 ? '#28a745' : '#adb5bd';
            },
            pointBorderColor: '#fff',
            pointRadius: 5,
            pointHoverRadius: 7,
            pointStyle: (context) => {
              // Circle for readings taken, cross for no readings
              const value = context.raw.y;
              return value === 1 ? 'circle' : 'crossRot';
            },
            fill: false,
            tension: 0,
            stepped: 'before'
          };
        } else {
          // Default styling for other metrics
          return {
            label: serie.name,
            data: serie.data.map(point => ({
              x: new Date(point.x),
              y: point.y
            })),
            borderColor: '#B71540',
            backgroundColor: 'rgba(183, 21, 64, 0.1)',
            borderWidth: 2,
            pointBackgroundColor: '#B71540',
            pointBorderColor: '#fff',
            pointRadius: 4,
            pointHoverRadius: 6,
            fill: false,
            tension: 0.4
          };
        }
      });

      // Add threshold lines if provided
      if (props.thresholds) {
        // Add upper threshold if it exists
        if (props.thresholds.upper !== undefined) {
          datasets.push(createThresholdDataset(
            `Upper ${props.yAxisLabel} Threshold`,
            props.thresholds.upper,
            'rgba(255, 0, 0, 0.5)',
            startDate,
            endDate
          ));
        }

        // Add lower threshold if it exists
        if (props.thresholds.lower !== undefined) {
          datasets.push(createThresholdDataset(
            `Lower ${props.yAxisLabel} Threshold`,
            props.thresholds.lower,
            'rgba(255, 0, 0, 0.5)',
            startDate,
            endDate
          ));
        }
      }

      return {
        datasets
      };
    });

    const chartOptions = computed(() => {
      const { startDate, endDate } = getDateRange();

      return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              title: (tooltipItems) => {
                const date = new Date(tooltipItems[0].parsed.x);
                return date.toLocaleString();
              },
              label: (context) => {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }

                // Format the value based on metric type
                let value = context.parsed.y;
                if (props.metricType === 'spirometry') {
                  label += `${value.toFixed(2)} L`;
                } else if (props.metricType === 'respiratoryRate') {
                  label += `${value} bpm`;
                } else if (props.metricType === 'oxygenSaturation') {
                  label += `${value}%`;
                } else if (props.metricType === 'heartRate') {
                  label += `${value} bpm`;
                } else if (props.metricType === 'temperature') {
                  label += `${value}°C`;
                } else if (props.metricType === 'readingPresence') {
                  label = value === 1 ? 'Reading taken' : 'No reading';
                } else {
                  label += value;
                }

                return label;
              }
            }
          },
          legend: {
            display: true,
            position: 'top'
          }
        },
        scales: {
          x: {
            type: 'time',
            time: {
              unit: 'day',
              displayFormats: {
                day: 'MMM d'
              }
            },
            min: startDate,
            max: endDate,
            title: {
              display: true,
              text: 'Date'
            }
          },
          y: {
            title: {
              display: !!props.yAxisLabel,
              text: props.yAxisLabel
            },
            beginAtZero: props.metricType === 'readingPresence',
            min: props.metricType === 'readingPresence' ? 0 : undefined,
            max: props.metricType === 'readingPresence' ? 1 : undefined,
            ticks: {
              stepSize: props.metricType === 'readingPresence' ? 1 : undefined,
              callback: function(value) {
                if (props.metricType === 'readingPresence') {
                  return value === 1 ? 'Yes' : 'No';
                }
                return value;
              }
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        },
        animations: {
          tension: {
            duration: 1000,
            easing: 'linear'
          }
        }
      };
    });

    const createThresholdDataset = (label, value, color, startDate, endDate) => {
      // Create a dataset for a threshold line
      const seriesData = resolvedSeries.value || props.series;

      if (!hasData.value || !seriesData[0]?.data?.length) {
        return {
          label,
          data: [],
          borderColor: color,
          borderWidth: 2,
          borderDash: [5, 5],
          pointRadius: 0,
          fill: false
        };
      }

      // Create a flat line at the threshold value with two points (start and end)
      return {
        label,
        data: [
          { x: startDate, y: value },
          { x: endDate, y: value }
        ],
        borderColor: color,
        borderWidth: 2,
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      };
    };

    const changeTimeRange = (range) => {
      selectedTimeRange.value = range;
    };

    // Function to render the chart with retry capability
    const renderChart = (retryCount = 0, maxRetries = 5) => {
      try {
        // First, check if we have data to render
        if (!hasData.value) {
          console.log(`No data available for chart ${chartId}, skipping render`);
          return;
        }

        // Get the chart canvas element by ID
        const canvas = document.getElementById(chartId);
        if (!canvas) {
          console.error(`Canvas with ID ${chartId} not found (attempt ${retryCount + 1}/${maxRetries})`);

          // If we haven't exceeded max retries, try again after a delay
          if (retryCount < maxRetries) {
            console.log(`Retrying render for chart ${chartId} in ${(retryCount + 1) * 200}ms...`);
            setTimeout(() => renderChart(retryCount + 1, maxRetries), (retryCount + 1) * 200);
            return;
          } else {
            console.error(`Max retries exceeded for chart ${chartId}. Canvas not found.`);
            chartError.value = "Could not render chart: Canvas element not found";
            return;
          }
        }

        // Make sure the canvas is visible and has dimensions
        const canvasRect = canvas.getBoundingClientRect();
        if (canvasRect.width === 0 || canvasRect.height === 0) {
          console.warn(`Canvas with ID ${chartId} has zero dimensions, deferring chart creation`);

          // If we haven't exceeded max retries, try again after a delay
          if (retryCount < maxRetries) {
            console.log(`Retrying render for chart ${chartId} in ${(retryCount + 1) * 200}ms...`);
            setTimeout(() => renderChart(retryCount + 1, maxRetries), (retryCount + 1) * 200);
            return;
          } else {
            console.error(`Max retries exceeded for chart ${chartId}. Canvas has zero dimensions.`);
            chartError.value = "Could not render chart: Canvas has zero dimensions";
            return;
          }
        }

        // Verify the canvas context is available
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          console.error(`Failed to get 2D context for canvas ${chartId}`);
          return;
        }

        // Ensure any existing chart is properly destroyed
        destroyChart();

        // Validate chart data before creating the chart
        const data = chartData.value;
        if (!data || !data.datasets || !Array.isArray(data.datasets)) {
          console.error(`Invalid chart data for ${chartId}:`, data);
          return;
        }

        // Check if any dataset has invalid data
        for (const dataset of data.datasets) {
          if (!dataset || !dataset.data || !Array.isArray(dataset.data)) {
            console.error(`Invalid dataset in chart ${chartId}:`, dataset);
            return;
          }
        }

        // Create a new chart instance with a try-catch block
        try {
          console.log(`Creating new chart instance for ${chartId}`);

          // Use the verified context instead of the canvas element
          chartInstance.value = new ChartJS(ctx, {
            type: 'line',
            data: data,
            options: chartOptions.value
          });

          // Verify the chart was created successfully
          if (!chartInstance.value) {
            console.error(`Failed to create chart instance for ${chartId}`);
            return;
          }

          console.log(`Chart ${chartId} created successfully`);
        } catch (error) {
          console.error(`Error creating chart instance for ${chartId}:`, error);
          chartInstance.value = null;
        }
      } catch (error) {
        console.error(`Error in renderChart function for ${chartId}:`, error);
      }
    };

    // Initialize the chart when component is mounted
    onMounted(() => {
      // Use a delay to ensure the DOM is fully rendered
      setTimeout(() => {
        nextTick(() => {
          console.log(`Initializing chart ${chartId} after component mount`);
          renderChart();
        });
      }, 200);
    });

    // Clean up chart when component is unmounted
    onBeforeUnmount(() => {
      destroyChart();
    });

    // Watch for changes in the series data
    watch(() => props.series, (newSeries, oldSeries) => {
      try {
        console.log(`Series data changed for chart ${chartId}`);

        // Validate the new series data
        if (!newSeries || !Array.isArray(newSeries) || newSeries.length === 0) {
          console.warn(`New series data is invalid for chart ${chartId}:`, newSeries);
          return;
        }

        // Destroy the chart first
        destroyChart();

        // Use the debounced render function to prevent rapid chart recreation
        debouncedRender();
      } catch (error) {
        console.error(`Error in series watcher for chart ${chartId}:`, error);
      }
    }, { deep: true });

    return {
      hasData,
      chartData,
      chartOptions,
      timeRangeOptions,
      selectedTimeRange,
      changeTimeRange,
      chartInstance,
      renderChart,
      chartId,
      destroyChart,
      chartError,
      props // Expose props to the template
    };
  }
};
</script>

<style scoped lang="scss">
.time-series-chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .chart-title {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
  }

  .chart-wrapper {
    position: relative;
    height: 100%;
  }

  .no-data-message {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px dashed #ddd;
    padding: 20px;

    .no-data-icon {
      position: relative;
      margin-bottom: 15px;

      i {
        font-size: 36px;
        color: #aaa;
      }

      .overlay-icon {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 48px;
        color: #d9534f;
        opacity: 0.7;
      }
    }

    p {
      margin: 5px 0;
      text-align: center;
    }

    .no-data-hint {
      font-size: 0.9em;
      color: #999;
      font-style: italic;
    }
  }

  .chart-error {
    margin-top: 10px;
    padding: 10px;
    background-color: rgba(217, 83, 79, 0.1);
    border-left: 4px solid #d9534f;
    color: #d9534f;
    font-size: 0.9em;
    border-radius: 4px;
  }

  .chart-loading {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;

    .spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(183, 21, 64, 0.3);
      border-radius: 50%;
      border-top-color: #B71540;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  }
}
</style>
