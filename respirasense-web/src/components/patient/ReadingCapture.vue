<template>
  <div class="reading-capture">
    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: `${(currentStep / (steps.length - 1)) * 100}%` }"
        ></div>
      </div>
      <div class="step-indicators">
        <div
          v-for="(step, index) in steps"
          :key="step.id"
          class="step-indicator"
          :class="{
            active: index === currentStep,
            completed: index < currentStep
          }"
        >
          <i :class="step.icon"></i>
        </div>
      </div>
    </div>

    <!-- Step Content -->
    <div class="step-content">
      <div class="step-header">
        <h2>{{ currentStepData.title }}</h2>
        <p>{{ currentStepData.description }}</p>
      </div>

      <!-- Welcome Step -->
      <div v-if="currentStepData.id === 'welcome'" class="welcome-step">
        <div class="welcome-content">
          <div class="icon-large">
            <i class="fas fa-stethoscope"></i>
          </div>
          <h3>Health Reading Session</h3>
          <p>We'll guide you through taking your health readings using connected sensors.</p>

          <div class="checklist">
            <div class="checklist-item" :class="{ completed: serverAvailable }">
              <i :class="serverAvailable ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
              <span>AI Server Connection</span>
              <button v-if="!serverAvailable" @click="checkServerHealth" :disabled="isLoading" class="retry-btn">
                {{ isLoading ? 'Checking...' : 'Retry' }}
              </button>
            </div>
          </div>

          <div v-if="error" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
          </div>
        </div>
      </div>

      <!-- Temperature Step -->
      <div v-else-if="currentStepData.id === 'temperature'" class="temperature-step">
        <div class="reading-content">
          <div class="sensor-icon">
            <i class="fas fa-thermometer-half"></i>
          </div>

          <div v-if="!readings.temperature" class="instructions">
            <h3>Temperature Reading</h3>
            <p>Position your finger on the sensor and click the button below to take a reading.</p>
            <button @click="takeTemperatureReading" :disabled="isLoading" class="reading-btn">
              {{ isLoading ? 'Reading...' : 'Take Temperature Reading' }}
            </button>
          </div>

          <div v-else class="reading-result">
            <h3>Temperature Reading Complete</h3>
            <div class="result-value">
              {{ readings.temperature.temperature.toFixed(1) }}°C
            </div>
            <p class="result-info">Reading taken at {{ formatTime(readings.temperature.timestamp) }}</p>
            <button @click="takeTemperatureReading" :disabled="isLoading" class="retry-reading-btn">
              {{ isLoading ? 'Reading...' : 'Retake Reading' }}
            </button>
          </div>

          <div v-if="error" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
          </div>
        </div>
      </div>

      <!-- Oximetry Step -->
      <div v-else-if="currentStepData.id === 'oximetry'" class="oximetry-step">
        <div class="reading-content">
          <div class="sensor-icon">
            <i class="fas fa-heartbeat"></i>
          </div>

          <div v-if="!readings.oximetry" class="instructions">
            <h3>Pulse Oximetry Reading</h3>
            <p>Place your finger in the pulse oximeter and click the button below to measure your SpO2 and heart rate.</p>
            <button @click="takeOximetryReading" :disabled="isLoading" class="reading-btn">
              {{ isLoading ? 'Reading...' : 'Take Oximetry Reading' }}
            </button>
          </div>

          <div v-else class="reading-result">
            <h3>Oximetry Reading Complete</h3>
            <div class="result-grid">
              <div class="result-item">
                <div class="result-label">SpO2</div>
                <div class="result-value">{{ readings.oximetry.spo2.toFixed(1) }}%</div>
              </div>
              <div class="result-item">
                <div class="result-label">Heart Rate</div>
                <div class="result-value">{{ readings.oximetry.heartRate.toFixed(0) }} bpm</div>
              </div>
            </div>
            <p class="result-info">Reading taken at {{ formatTime(readings.oximetry.timestamp) }}</p>
            <button @click="takeOximetryReading" :disabled="isLoading" class="retry-reading-btn">
              {{ isLoading ? 'Reading...' : 'Retake Reading' }}
            </button>
          </div>

          <div v-if="error" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
          </div>
        </div>
      </div>

      <!-- Spirometry Step -->
      <div v-else-if="currentStepData.id === 'spirometry'" class="spirometry-step">
        <div class="reading-content">
          <div class="sensor-icon">
            <i class="fas fa-lungs"></i>
          </div>

          <div v-if="!readings.spirometry" class="instructions">
            <h3>Spirometry Test</h3>
            <p>Connect the spirometer and follow the breathing instructions. Take a deep breath and blow as hard and fast as you can.</p>
            <button @click="takeSpirometryReading" :disabled="isLoading" class="reading-btn">
              {{ isLoading ? 'Testing...' : 'Start Spirometry Test' }}
            </button>
          </div>

          <div v-else class="reading-result">
            <h3>Spirometry Test Complete</h3>
            <div class="result-grid">
              <div class="result-item">
                <div class="result-label">FEV1</div>
                <div class="result-value">{{ readings.spirometry.FEV1?.toFixed(2) || '--' }} L</div>
              </div>
              <div class="result-item">
                <div class="result-label">FVC</div>
                <div class="result-value">{{ readings.spirometry.FVC?.toFixed(2) || '--' }} L</div>
              </div>
              <div class="result-item">
                <div class="result-label">PEF</div>
                <div class="result-value">{{ readings.spirometry.PEF?.toFixed(1) || '--' }} L/min</div>
              </div>
              <div class="result-item">
                <div class="result-label">FEV1/FVC</div>
                <div class="result-value">{{ readings.spirometry['FEV1/FVC']?.toFixed(1) || '--' }}%</div>
              </div>
            </div>
            <p class="result-info">Test completed at {{ formatTime(readings.spirometry.timestamp) }}</p>
            <button @click="takeSpirometryReading" :disabled="isLoading" class="retry-reading-btn">
              {{ isLoading ? 'Testing...' : 'Retake Test' }}
            </button>
          </div>

          <div v-if="error" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
          </div>
        </div>
      </div>

      <!-- Results Step -->
      <div v-else-if="currentStepData.id === 'results'" class="results-step">
        <div class="results-content">
          <div v-if="isLoading" class="loading-prediction">
            <div class="spinner"></div>
            <p>Analyzing your readings...</p>
          </div>

          <div v-else-if="readings.prediction" class="prediction-results">
            <div class="prediction-header">
              <div class="risk-indicator" :class="getRiskClass(readings.prediction.probability)">
                <i :class="getRiskIcon(readings.prediction.probability)"></i>
              </div>
              <h3>COPD Risk Assessment</h3>
            </div>

            <div class="prediction-summary">
              <div class="diagnosis">
                <span class="label">Diagnosis:</span>
                <span class="value" :class="readings.prediction.diagnosis.toLowerCase()">
                  {{ readings.prediction.diagnosis }}
                </span>
              </div>
              <div class="probability">
                <span class="label">Risk Probability:</span>
                <span class="value">{{ (readings.prediction.probability * 100).toFixed(1) }}%</span>
              </div>
              <div class="confidence">
                <span class="label">Confidence:</span>
                <span class="value">{{ readings.prediction.confidence }}</span>
              </div>
            </div>

            <div class="readings-summary">
              <h4>Reading Summary</h4>
              <div class="summary-grid">
                <div class="summary-item">
                  <span class="label">Temperature:</span>
                  <span class="value">{{ readings.temperature?.temperature?.toFixed(1) }}°C</span>
                </div>
                <div class="summary-item">
                  <span class="label">SpO2:</span>
                  <span class="value">{{ readings.oximetry?.spo2?.toFixed(1) }}%</span>
                </div>
                <div class="summary-item">
                  <span class="label">Heart Rate:</span>
                  <span class="value">{{ readings.oximetry?.heartRate?.toFixed(0) }} bpm</span>
                </div>
                <div class="summary-item">
                  <span class="label">FEV1:</span>
                  <span class="value">{{ readings.spirometry?.FEV1?.toFixed(2) }} L</span>
                </div>
              </div>
            </div>
          </div>

          <div v-if="error" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
            <button @click="retryCurrentStep" class="retry-btn">Retry Analysis</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="navigation">
      <button
        @click="cancelReading"
        class="nav-btn cancel-btn"
      >
        Cancel
      </button>

      <div class="nav-right">
        <button
          v-if="!isFirstStep"
          @click="previousStep"
          class="nav-btn secondary-btn"
          :disabled="isLoading"
        >
          Previous
        </button>

        <button
          v-if="!isLastStep"
          @click="nextStep"
          class="nav-btn primary-btn"
          :disabled="!canProceed || isLoading"
        >
          Next
        </button>

        <button
          v-if="isLastStep && readings.prediction"
          @click="saveReading"
          class="nav-btn primary-btn"
          :disabled="isLoading"
        >
          {{ isLoading ? 'Saving...' : 'Save Reading' }}
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  @import '@/styles/components/patient/ReadingCapture.scss';
</style>

<script>
import { useReadingCapture } from '@/composables/useReadingCapture';
import { onMounted } from 'vue';

export default {
  name: 'ReadingCapture',

  setup() {
    const capture = useReadingCapture();

    // Helper methods
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString();
    };

    const getRiskClass = (probability) => {
      if (probability >= 0.7) return 'high-risk';
      if (probability >= 0.4) return 'medium-risk';
      return 'low-risk';
    };

    const getRiskIcon = (probability) => {
      if (probability >= 0.7) return 'fas fa-exclamation-triangle';
      if (probability >= 0.4) return 'fas fa-exclamation-circle';
      return 'fas fa-check-circle';
    };

    // Initialize
    onMounted(async () => {
      await capture.loadUserProfile();
      await capture.checkServerHealth();
    });

    return {
      ...capture,
      formatTime,
      getRiskClass,
      getRiskIcon
    };
  }
};
</script>
