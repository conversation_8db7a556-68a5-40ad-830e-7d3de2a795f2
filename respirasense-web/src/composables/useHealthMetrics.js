import { computed } from 'vue';

/**
 * Composable for health metrics functionality
 * @param {Object} options - Options object
 * @param {Object} options.lastReading - Ref to the last reading
 * @returns {Object} Health metrics utility functions and computed properties
 */
export function useHealthMetrics({ lastReading }) {
  /**
   * Get the COPD risk level from the last reading
   */
  const copdRiskLevel = computed(() => lastReading.value?.riskLevel || 'normal');

  /**
   * Get the status icon based on the COPD risk level
   */
  const getStatusIcon = computed(() => ({
    'fas fa-check-circle': copdRiskLevel.value === 'normal',
    'fas fa-exclamation-triangle': copdRiskLevel.value === 'warning',
    'fas fa-exclamation-circle': copdRiskLevel.value === 'danger'
  }));

  /**
   * Get the status message based on the COPD risk level
   */
  const getStatusMessage = computed(() => {
    const messages = {
      normal: 'Your readings are within normal range.',
      warning: 'Some metrics require attention. Consider consulting your doctor.',
      danger: 'Immediate medical attention may be required.'
    };
    return messages[copdRiskLevel.value] || messages.normal;
  });

  /**
   * Map risk levels from different formats
   * @param {string} riskLevel - The risk level to map
   * @returns {string} Mapped risk level
   */
  const mapRiskLevel = (riskLevel) => {
    if (!riskLevel) return 'normal';

    const riskMap = {
      'low': 'normal',
      'medium': 'warning',
      'high': 'danger',
      'normal': 'normal',
      'warning': 'warning',
      'danger': 'danger'
    };

    return riskMap[riskLevel.toLowerCase()] || 'normal';
  };

  /**
   * Get the icon for a metric
   * @param {string} metricKey - The metric key
   * @returns {string} Icon class
   */
  const getMetricIcon = (metricKey) => {
    const icons = {
      spirometry: 'fas fa-lungs',
      respiratoryRate: 'fas fa-lungs', // Keep for backward compatibility
      oxygenSaturation: 'fas fa-wind',
      heartRate: 'fas fa-heartbeat',
      temperature: 'fas fa-thermometer-half',
      // Specific spirometry metrics
      FEV1: 'fas fa-lungs',
      FVC: 'fas fa-lungs',
      FEV1_FVC: 'fas fa-percentage',
      PEF: 'fas fa-wind',
      FEF25_75: 'fas fa-chart-line'
    };
    return icons[metricKey] || 'fas fa-chart-line';
  };

  /**
   * Get spirometry metric display information
   * @param {Object} spirometry - Spirometry data object
   * @returns {Object} Formatted spirometry display data
   */
  const getSpirometryDisplay = (spirometry) => {
    if (!spirometry) return null;

    return {
      FEV1: {
        value: spirometry.FEV1?.toFixed(2) || '--',
        unit: 'L',
        label: 'FEV₁',
        description: 'Forced Expiratory Volume in 1 second'
      },
      FVC: {
        value: spirometry.FVC?.toFixed(2) || '--',
        unit: 'L',
        label: 'FVC',
        description: 'Forced Vital Capacity'
      },
      FEV1_FVC: {
        value: spirometry.FEV1_FVC ? (spirometry.FEV1_FVC * 100).toFixed(1) : '--',
        unit: '%',
        label: 'FEV₁/FVC',
        description: 'FEV₁/FVC Ratio'
      },
      PEF: {
        value: spirometry.PEF?.toFixed(1) || '--',
        unit: 'L/min',
        label: 'PEF',
        description: 'Peak Expiratory Flow'
      },
      percent_predicted: {
        value: spirometry.percent_predicted || '--',
        unit: '%',
        label: '% Predicted',
        description: 'Percentage of predicted normal value'
      }
    };
  };

  /**
   * Get the standard metrics configuration - now prioritizing spirometry
   */
  const metrics = [
    { key: 'spirometry', label: 'Spirometry', unit: 'L', primary: 'FEV1' },
    { key: 'oxygenSaturation', label: 'Oxygen Saturation', unit: '%' },
    { key: 'heartRate', label: 'Heart Rate', unit: 'bpm' },
    { key: 'temperature', label: 'Temperature', unit: '°C' }
  ];

  /**
   * Legacy metrics for backward compatibility
   */
  const legacyMetrics = [
    { key: 'respiratoryRate', label: 'Respiratory Rate', unit: 'bpm' },
    { key: 'oxygenSaturation', label: 'Oxygen Saturation', unit: '%' },
    { key: 'heartRate', label: 'Heart Rate', unit: 'bpm' },
    { key: 'temperature', label: 'Temperature', unit: '°C' }
  ];

  return {
    copdRiskLevel,
    getStatusIcon,
    getStatusMessage,
    mapRiskLevel,
    getMetricIcon,
    getSpirometryDisplay,
    metrics,
    legacyMetrics
  };
}
