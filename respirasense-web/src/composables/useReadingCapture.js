import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import aiServerService from '@/services/aiServerService';
import { auth } from '@/plugins/firebase/firebase';
import request from '@/Rest';

/**
 * Composable for managing the step-by-step reading capture process
 */
export function useReadingCapture() {
  const store = useStore();
  const router = useRouter();

  // State
  const currentStep = ref(0);
  const isLoading = ref(false);
  const error = ref(null);
  const serverAvailable = ref(false);

  // Reading data
  const readings = ref({
    temperature: null,
    oximetry: null,
    spirometry: null,
    prediction: null
  });

  // User profile data
  const userProfile = ref({
    age: '',
    gender: '',
    height: '',
    weight: ''
  });

  // Steps configuration
  const steps = [
    {
      id: 'welcome',
      title: 'Welcome',
      description: 'Prepare for your health reading session',
      icon: 'fas fa-hand-wave'
    },
    {
      id: 'temperature',
      title: 'Temperature',
      description: 'Measure your body temperature',
      icon: 'fas fa-thermometer-half'
    },
    {
      id: 'oximetry',
      title: 'Pulse Oximetry',
      description: 'Measure your SpO2 and heart rate',
      icon: 'fas fa-heartbeat'
    },
    {
      id: 'spirometry',
      title: 'Spirometry',
      description: 'Perform breathing test',
      icon: 'fas fa-lungs'
    },
    {
      id: 'results',
      title: 'Results',
      description: 'Review your COPD risk assessment',
      icon: 'fas fa-chart-line'
    }
  ];

  // Computed
  const currentStepData = computed(() => steps[currentStep.value]);
  const isFirstStep = computed(() => currentStep.value === 0);
  const isLastStep = computed(() => currentStep.value === steps.length - 1);
  const canProceed = computed(() => {
    const step = steps[currentStep.value];
    switch (step.id) {
      case 'welcome':
        return serverAvailable.value;
      case 'temperature':
        return readings.value.temperature !== null;
      case 'oximetry':
        return readings.value.oximetry !== null;
      case 'spirometry':
        return readings.value.spirometry !== null;
      case 'results':
        return readings.value.prediction !== null;
      default:
        return true;
    }
  });

  // Methods
  const loadUserProfile = async () => {
    try {
      const user = auth.currentUser;
      if (!user) throw new Error('No authenticated user');

      const patientDoc = await request.GET(`patients/${user.uid}`).Execute();
      if (patientDoc.exists()) {
        const data = patientDoc.data();
        userProfile.value = {
          age: data.age || '',
          gender: data.gender || '',
          height: data.height || '',
          weight: data.weight || ''
        };
      }
    } catch (err) {
      console.error('Error loading user profile:', err);
      error.value = 'Failed to load user profile';
    }
  };

  const checkServerHealth = async () => {
    try {
      isLoading.value = true;
      await aiServerService.checkHealth();
      serverAvailable.value = true;
      error.value = null;
    } catch (err) {
      serverAvailable.value = false;
      error.value = 'AI Server is not available. Please ensure the server is running on localhost:5000';
    } finally {
      isLoading.value = false;
    }
  };

  const takeTemperatureReading = async () => {
    try {
      isLoading.value = true;
      error.value = null;
      const result = await aiServerService.getTemperature();
      readings.value.temperature = result;
    } catch (err) {
      error.value = err.message || 'Failed to get temperature reading';
    } finally {
      isLoading.value = false;
    }
  };

  const takeOximetryReading = async () => {
    try {
      isLoading.value = true;
      error.value = null;
      const result = await aiServerService.getOximetry();
      readings.value.oximetry = result;
    } catch (err) {
      error.value = err.message || 'Failed to get oximetry reading';
    } finally {
      isLoading.value = false;
    }
  };

  const takeSpirometryReading = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      // Prepare demographics for spirometry
      const demographics = {
        age: parseInt(userProfile.value.age),
        height_cm: parseInt(userProfile.value.height),
        gender: userProfile.value.gender?.toLowerCase(),
        weight_kg: parseInt(userProfile.value.weight)
      };

      const result = await aiServerService.getSpirometry(demographics);
      readings.value.spirometry = result;
    } catch (err) {
      error.value = err.message || 'Failed to get spirometry reading';
    } finally {
      isLoading.value = false;
    }
  };

  const generatePrediction = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      // Prepare patient data for COPD prediction
      const patientData = aiServerService.preparePatientData(userProfile.value, readings.value);
      const result = await aiServerService.predictCOPD(patientData);
      readings.value.prediction = result;
    } catch (err) {
      error.value = err.message || 'Failed to generate COPD prediction';
    } finally {
      isLoading.value = false;
    }
  };

  const nextStep = async () => {
    if (currentStep.value < steps.length - 1) {
      currentStep.value++;

      // Auto-generate prediction when reaching results step
      if (steps[currentStep.value].id === 'results' && !readings.value.prediction) {
        await generatePrediction();
      }
    }
  };

  const previousStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  };

  const saveReading = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      // Prepare complete reading data
      const completeReading = {
        temperature: readings.value.temperature?.temperature,
        oxygenSaturation: readings.value.oximetry?.spo2,
        heartRate: readings.value.oximetry?.heartRate,
        respiratoryRate: readings.value.spirometry?.FEV1 ? Math.round(readings.value.spirometry.FEV1 * 10) : null,
        spirometry: readings.value.spirometry,
        copdPrediction: readings.value.prediction,
        timestamp: new Date(),
        riskLevel: getRiskLevel(readings.value.prediction?.probability || 0)
      };

      // Save to store
      await store.dispatch('patient/health/saveCompleteReading', completeReading);

      // Navigate back to dashboard
      router.push('/patient/dashboard');
    } catch (err) {
      error.value = err.message || 'Failed to save reading';
    } finally {
      isLoading.value = false;
    }
  };

  const getRiskLevel = (probability) => {
    if (probability >= 0.7) return 'danger';
    if (probability >= 0.4) return 'warning';
    return 'normal';
  };

  const cancelReading = () => {
    router.push('/patient/dashboard');
  };

  const retryCurrentStep = async () => {
    const step = steps[currentStep.value];
    switch (step.id) {
      case 'welcome':
        await checkServerHealth();
        break;
      case 'temperature':
        await takeTemperatureReading();
        break;
      case 'oximetry':
        await takeOximetryReading();
        break;
      case 'spirometry':
        await takeSpirometryReading();
        break;
      case 'results':
        await generatePrediction();
        break;
    }
  };

  return {
    // State
    currentStep,
    isLoading,
    error,
    serverAvailable,
    readings,
    userProfile,
    steps,

    // Computed
    currentStepData,
    isFirstStep,
    isLastStep,
    canProceed,

    // Methods
    loadUserProfile,
    checkServerHealth,
    takeTemperatureReading,
    takeOximetryReading,
    takeSpirometryReading,
    generatePrediction,
    nextStep,
    previousStep,
    saveReading,
    cancelReading,
    retryCurrentStep
  };
}
