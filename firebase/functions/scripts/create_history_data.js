const admin = require('firebase-admin');
const serviceAccount = require('./respirasense-ke-firebase-adminsdk-fbsvc-b7a690fe3e.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

/**
 * <PERSON>ript to create history data from individual readings
 */
async function createHistoryData() {
  try {
    console.log('🔄 Creating history data from individual readings...\n');
    
    // Get all patients
    const patientsSnapshot = await db.collection('patients').get();
    
    for (const patientDoc of patientsSnapshot.docs) {
      const patientId = patientDoc.id;
      const patientData = patientDoc.data();
      console.log(`📋 Processing patient: ${patientId} (${patientData.name || 'Unknown'})`);
      
      // Get all readings for this patient
      const readingsSnapshot = await db.collection(`patients/${patientId}/readings`)
        .orderBy('timestamp', 'desc')
        .get();
      
      if (readingsSnapshot.docs.length === 0) {
        console.log(`   ⚠️  No readings found for patient ${patientId}`);
        continue;
      }
      
      console.log(`   📊 Found ${readingsSnapshot.docs.length} readings`);
      
      // Process readings into history format
      const respiratoryRateHistory = [];
      const oxygenSaturationHistory = [];
      const heartRateHistory = [];
      const temperatureHistory = [];
      const spirometryHistory = [];
      
      readingsSnapshot.docs.forEach(doc => {
        const reading = doc.data();
        const timestamp = reading.timestamp;
        
        // Add to respiratory rate history
        if (reading.respiratoryRate !== null && reading.respiratoryRate !== undefined) {
          respiratoryRateHistory.push({
            x: timestamp,
            y: reading.respiratoryRate
          });
        }
        
        // Add to oxygen saturation history
        if (reading.oxygenSaturation !== null && reading.oxygenSaturation !== undefined) {
          oxygenSaturationHistory.push({
            x: timestamp,
            y: reading.oxygenSaturation
          });
        }
        
        // Add to heart rate history
        if (reading.heartRate !== null && reading.heartRate !== undefined) {
          heartRateHistory.push({
            x: timestamp,
            y: reading.heartRate
          });
        }
        
        // Add to temperature history
        if (reading.temperature !== null && reading.temperature !== undefined) {
          temperatureHistory.push({
            x: timestamp,
            y: reading.temperature
          });
        }
        
        // Add to spirometry history
        if (reading.spirometry) {
          spirometryHistory.push({
            timestamp: timestamp,
            spirometry: reading.spirometry
          });
        }
      });
      
      // Create the history document
      const historyData = {
        respiratoryRate: respiratoryRateHistory,
        oxygenSaturation: oxygenSaturationHistory,
        heartRate: heartRateHistory,
        temperature: temperatureHistory,
        spirometry: spirometryHistory,
        lastUpdated: new Date()
      };
      
      // Save to Firestore
      await db.doc(`patients/${patientId}/health_records/history`).set(historyData);
      
      console.log(`   ✅ Created history document with:`);
      console.log(`      - Respiratory Rate: ${respiratoryRateHistory.length} points`);
      console.log(`      - Oxygen Saturation: ${oxygenSaturationHistory.length} points`);
      console.log(`      - Heart Rate: ${heartRateHistory.length} points`);
      console.log(`      - Temperature: ${temperatureHistory.length} points`);
      console.log(`      - Spirometry: ${spirometryHistory.length} points`);
    }
    
    console.log('\n✅ History data creation completed successfully!');
    
  } catch (error) {
    console.error('❌ Error creating history data:', error);
    throw error;
  }
}

// Run the script
createHistoryData()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
