const admin = require('firebase-admin');
const serviceAccount = require('./respirasense-ke-firebase-adminsdk-fbsvc-b7a690fe3e.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

/**
 * <PERSON>ript to test data access and see what's in the health_records
 */
async function testDataAccess() {
  try {
    console.log('🔍 Testing data access for Test User Alpha...\n');
    
    // Get the Test User Alpha patient ID (we know it from the inspection)
    const patientsSnapshot = await db.collection('patients').get();
    
    let testUserAlphaId = null;
    for (const patientDoc of patientsSnapshot.docs) {
      const patientData = patientDoc.data();
      if (patientData.name === 'Test User Alpha') {
        testUserAlphaId = patientDoc.id;
        break;
      }
    }
    
    if (!testUserAlphaId) {
      console.log('❌ Test User Alpha not found');
      return;
    }
    
    console.log(`✅ Found Test User Alpha with ID: ${testUserAlphaId}\n`);
    
    // Check health_records/readings document
    console.log('📋 Checking health_records/readings document...');
    const readingsDoc = await db.doc(`patients/${testUserAlphaId}/health_records/readings`).get();
    
    if (readingsDoc.exists) {
      const readingsData = readingsDoc.data();
      console.log('✅ health_records/readings document exists');
      console.log('📊 Latest reading:', JSON.stringify(readingsData.latest, null, 2));
      console.log('📊 Recent readings count:', readingsData.recent ? readingsData.recent.length : 0);
      
      if (readingsData.latest && readingsData.latest.spirometry) {
        console.log('✅ Latest reading has spirometry data!');
        console.log('🫁 Spirometry data:', JSON.stringify(readingsData.latest.spirometry, null, 2));
      } else {
        console.log('❌ Latest reading does NOT have spirometry data');
      }
    } else {
      console.log('❌ health_records/readings document does NOT exist');
    }
    
    console.log('\n📋 Checking health_records/history document...');
    const historyDoc = await db.doc(`patients/${testUserAlphaId}/health_records/history`).get();
    
    if (historyDoc.exists) {
      const historyData = historyDoc.data();
      console.log('✅ health_records/history document exists');
      console.log('📊 History keys:', Object.keys(historyData));
      
      if (historyData.spirometry) {
        console.log('✅ History has spirometry data!');
        console.log('🫁 Spirometry history count:', historyData.spirometry.length);
      } else {
        console.log('❌ History does NOT have spirometry data');
      }
    } else {
      console.log('❌ health_records/history document does NOT exist');
    }
    
    console.log('\n📋 Checking individual readings collection...');
    const readingsCollection = await db.collection(`patients/${testUserAlphaId}/readings`)
      .orderBy('timestamp', 'desc')
      .limit(3)
      .get();
    
    console.log(`✅ Found ${readingsCollection.docs.length} individual readings`);
    
    readingsCollection.docs.forEach((doc, index) => {
      const reading = doc.data();
      console.log(`\n📊 Reading ${index + 1} (${doc.id}):`);
      console.log('   - Timestamp:', reading.timestamp);
      console.log('   - Respiratory Rate:', reading.respiratoryRate);
      console.log('   - Has spirometry:', !!reading.spirometry);
      
      if (reading.spirometry) {
        console.log('   - FEV1:', reading.spirometry.FEV1);
        console.log('   - FVC:', reading.spirometry.FVC);
        console.log('   - Ratio:', reading.spirometry.FEV1_FVC);
      }
    });
    
  } catch (error) {
    console.error('❌ Error testing data access:', error);
  }
}

// Run the test
testDataAccess()
  .then(() => {
    console.log('\n✅ Data access test completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Data access test failed:', error);
    process.exit(1);
  });
