const admin = require('firebase-admin');
const serviceAccount = require('./respirasense-ke-firebase-adminsdk-fbsvc-b7a690fe3e.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

/**
 * <PERSON>ript to inspect the current data structure in Firebase
 */
async function inspectData() {
  try {
    console.log('🔍 Inspecting current database structure...\n');
    
    // Get all patients
    const patientsSnapshot = await db.collection('patients').get();
    console.log(`Found ${patientsSnapshot.docs.length} patients\n`);
    
    for (const patientDoc of patientsSnapshot.docs) {
      const patientId = patientDoc.id;
      const patientData = patientDoc.data();
      console.log(`📋 Patient: ${patientId} (${patientData.name || 'Unknown'})`);
      
      // Check readings
      const readingsSnapshot = await db.collection(`patients/${patientId}/readings`).limit(3).get();
      console.log(`   📊 Readings found: ${readingsSnapshot.docs.length}`);
      
      if (readingsSnapshot.docs.length > 0) {
        console.log('   📝 Sample reading structure:');
        const sampleReading = readingsSnapshot.docs[0].data();
        console.log('   ', JSON.stringify(sampleReading, null, 2));
        
        // Check if it has respiratory rate or spirometry
        if (sampleReading.respiratoryRate) {
          console.log(`   ✅ Has respiratory rate: ${sampleReading.respiratoryRate}`);
        }
        if (sampleReading.spirometry) {
          console.log(`   ✅ Has spirometry data:`, sampleReading.spirometry);
        }
        if (!sampleReading.respiratoryRate && !sampleReading.spirometry) {
          console.log(`   ❌ No respiratory rate or spirometry data found`);
        }
      }
      
      // Check daily metrics
      const dailyMetricsSnapshot = await db.collection(`patients/${patientId}/daily_metrics`).limit(2).get();
      console.log(`   📈 Daily metrics found: ${dailyMetricsSnapshot.docs.length}`);
      
      if (dailyMetricsSnapshot.docs.length > 0) {
        const sampleMetric = dailyMetricsSnapshot.docs[0].data();
        console.log('   📝 Sample daily metric structure:');
        console.log('   ', JSON.stringify(sampleMetric, null, 2));
      }
      
      // Check profile metrics
      const profileMetricsRef = db.doc(`patients/${patientId}/profile/metrics`);
      const profileMetrics = await profileMetricsRef.get();
      
      if (profileMetrics.exists) {
        console.log('   👤 Profile metrics found:');
        const metrics = profileMetrics.data();
        console.log('   ', JSON.stringify(metrics, null, 2));
      } else {
        console.log('   ❌ No profile metrics found');
      }
      
      console.log(''); // Empty line for readability
    }
    
  } catch (error) {
    console.error('❌ Error inspecting data:', error);
  }
}

// Run the inspection
inspectData()
  .then(() => {
    console.log('✅ Data inspection completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Data inspection failed:', error);
    process.exit(1);
  });
