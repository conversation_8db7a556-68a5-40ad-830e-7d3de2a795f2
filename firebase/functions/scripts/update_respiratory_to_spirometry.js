const admin = require('firebase-admin');
const serviceAccount = require('./respirasense-ke-firebase-adminsdk-fbsvc-b7a690fe3e.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

/**
 * Enhanced script to update existing respiratory rate data to include spirometry test parameters
 * This will:
 * 1. Update all patient readings to include spirometry data
 * 2. Update daily metrics to include spirometry metrics
 * 3. Update patient profile metrics to include spirometry data
 * 4. Ensure data consistency across all collections
 * 5. Add proper spirometry thresholds and risk assessment
 */

/**
 * Generate realistic spirometry values based on demographics and respiratory rate
 * @param {number} respiratoryRate - Original respiratory rate
 * @param {Object} demographics - Patient demographics (age, height, gender)
 * @returns {Object} Spirometry data object
 */
function generateSpirometryData(respiratoryRate, demographics = {}) {
  // Base FEV1 calculation - more sophisticated than simple division
  // Normal FEV1 ranges: Men 3.5-4.5L, Women 2.5-3.5L
  const age = demographics.age || 35;
  const height = demographics.height || 170; // cm
  const isMale = demographics.gender === 'male';

  // Predicted FEV1 based on demographics (simplified formula)
  let predictedFEV1;
  if (isMale) {
    predictedFEV1 = (0.0414 * height) - (0.0244 * age) - 2.190;
  } else {
    predictedFEV1 = (0.0342 * height) - (0.0255 * age) - 1.578;
  }

  // Adjust based on respiratory rate (higher RR might indicate lower lung function)
  const rrFactor = Math.max(0.6, Math.min(1.2, (20 - respiratoryRate) / 10 + 1));
  const fev1 = Math.max(1.0, predictedFEV1 * rrFactor * (0.8 + Math.random() * 0.4));

  // FVC is typically 15-25% higher than FEV1
  const fvc = fev1 * (1.15 + Math.random() * 0.1);

  // FEV1/FVC ratio - normal is >0.7, COPD typically <0.7
  const fev1_fvc = Math.max(0.5, Math.min(0.9, fev1 / fvc + (Math.random() * 0.1 - 0.05)));

  // Peak Expiratory Flow - typically 4-8 times FEV1
  const pef = fev1 * (4 + Math.random() * 4);

  // FEF25-75 - typically 2-4 times FEV1
  const fef25_75 = fev1 * (2 + Math.random() * 2);

  return {
    FEV1: Math.round(fev1 * 100) / 100,
    FVC: Math.round(fvc * 100) / 100,
    FEV1_FVC: Math.round(fev1_fvc * 100) / 100,
    PEF: Math.round(pef * 100) / 100,
    FEF25_75: Math.round(fef25_75 * 100) / 100,
    timestamp: new Date(),
    predicted_FEV1: Math.round(predictedFEV1 * 100) / 100,
    percent_predicted: Math.round((fev1 / predictedFEV1) * 100)
  };
}

/**
 * Assess COPD risk based on spirometry values
 * @param {Object} spirometry - Spirometry data
 * @returns {string} Risk level
 */
function assessSpirometryRisk(spirometry) {
  const { FEV1_FVC, percent_predicted } = spirometry;

  // GOLD criteria for COPD assessment
  if (FEV1_FVC < 0.7) {
    if (percent_predicted >= 80) return 'mild';
    if (percent_predicted >= 50) return 'moderate';
    if (percent_predicted >= 30) return 'severe';
    return 'very_severe';
  }

  // Normal spirometry
  if (percent_predicted >= 80 && FEV1_FVC >= 0.7) return 'normal';

  // Borderline
  return 'borderline';
}

async function updateRespiratoryToSpirometry() {
  try {
    console.log('🔄 Starting enhanced respiratory data update to spirometry format...');

    // Get all patients
    const patientsSnapshot = await db.collection('patients').get();
    let totalPatientsProcessed = 0;
    let totalReadingsUpdated = 0;

    for (const patientDoc of patientsSnapshot.docs) {
      const patientId = patientDoc.id;
      const patientData = patientDoc.data();
      console.log(`\n📋 Processing patient: ${patientId} (${patientData.name || 'Unknown'})`);

      // Get patient demographics for better spirometry calculation
      const demographics = {
        age: patientData.age || 35,
        height: patientData.height || 170,
        gender: patientData.gender || 'male'
      };

      // Update readings
      const readingsSnapshot = await db.collection(`patients/${patientId}/readings`).get();
      let patientReadingsUpdated = 0;

      for (const readingDoc of readingsSnapshot.docs) {
        const reading = readingDoc.data();

        // Only update if it has respiratory rate but no spirometry data
        if (reading.respiratoryRate && !reading.spirometry) {
          // Generate spirometry data based on respiratory rate and demographics
          const spirometry = generateSpirometryData(reading.respiratoryRate, demographics);
          const spirometryRisk = assessSpirometryRisk(spirometry);

          // Update the reading document
          await readingDoc.ref.update({
            spirometry,
            spirometryRisk,
            // Keep original respiratory rate for backward compatibility
            respiratoryRate: reading.respiratoryRate,
            // Update overall risk level if spirometry indicates higher risk
            riskLevel: spirometryRisk === 'normal' ? (reading.riskLevel || 'normal') : spirometryRisk
          });

          patientReadingsUpdated++;
          totalReadingsUpdated++;
        }
      }

      console.log(`   ✅ Updated ${patientReadingsUpdated} readings for patient ${patientId}`);

      // Update daily metrics
      const dailyMetricsSnapshot = await db.collection(`patients/${patientId}/daily_metrics`).get();
      let dailyMetricsUpdated = 0;

      for (const metricDoc of dailyMetricsSnapshot.docs) {
        const metric = metricDoc.data();

        if (metric.respiratoryRate && !metric.spirometry) {
          const spirometry = generateSpirometryData(metric.respiratoryRate, demographics);
          const spirometryRisk = assessSpirometryRisk(spirometry);

          await metricDoc.ref.update({
            spirometry,
            spirometryRisk,
            riskLevel: spirometryRisk === 'normal' ? (metric.riskLevel || 'normal') : spirometryRisk
          });

          dailyMetricsUpdated++;
        }
      }

      if (dailyMetricsUpdated > 0) {
        console.log(`   ✅ Updated ${dailyMetricsUpdated} daily metrics for patient ${patientId}`);
      }

      // Update patient profile metrics
      const profileMetricsRef = db.doc(`patients/${patientId}/profile/metrics`);
      const profileMetrics = await profileMetricsRef.get();

      if (profileMetrics.exists) {
        const metrics = profileMetrics.data();

        if (metrics.latestReading && metrics.latestReading.respiratoryRate && !metrics.latestReading.spirometry) {
          const spirometry = generateSpirometryData(metrics.latestReading.respiratoryRate, demographics);
          const spirometryRisk = assessSpirometryRisk(spirometry);

          await profileMetricsRef.update({
            'latestReading.spirometry': spirometry,
            'latestReading.spirometryRisk': spirometryRisk,
            'latestReading.riskLevel': spirometryRisk === 'normal' ? (metrics.latestReading.riskLevel || 'normal') : spirometryRisk
          });

          console.log(`   ✅ Updated profile metrics for patient ${patientId}`);
        }
      }

      // Update health records
      const healthRecordsRef = db.doc(`patients/${patientId}/health_records/readings`);
      const healthRecords = await healthRecordsRef.get();

      if (healthRecords.exists) {
        const records = healthRecords.data();
        let updated = false;

        // Update latest reading
        if (records.latest && records.latest.respiratoryRate && !records.latest.spirometry) {
          const spirometry = generateSpirometryData(records.latest.respiratoryRate, demographics);
          const spirometryRisk = assessSpirometryRisk(spirometry);

          records.latest.spirometry = spirometry;
          records.latest.spirometryRisk = spirometryRisk;
          records.latest.riskLevel = spirometryRisk === 'normal' ? (records.latest.riskLevel || 'normal') : spirometryRisk;
          updated = true;
        }

        // Update recent readings
        if (records.recent && Array.isArray(records.recent)) {
          records.recent.forEach(reading => {
            if (reading.respiratoryRate && !reading.spirometry) {
              const spirometry = generateSpirometryData(reading.respiratoryRate, demographics);
              const spirometryRisk = assessSpirometryRisk(spirometry);

              reading.spirometry = spirometry;
              reading.spirometryRisk = spirometryRisk;
              reading.riskLevel = spirometryRisk === 'normal' ? (reading.riskLevel || 'normal') : spirometryRisk;
              updated = true;
            }
          });
        }

        if (updated) {
          await healthRecordsRef.set(records);
          console.log(`   ✅ Updated health records for patient ${patientId}`);
        }
      }

      totalPatientsProcessed++;
    }

    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📊 Summary:`);
    console.log(`   - Patients processed: ${totalPatientsProcessed}`);
    console.log(`   - Total readings updated: ${totalReadingsUpdated}`);
    console.log(`   - Enhanced spirometry data with demographics-based calculations`);
    console.log(`   - Added COPD risk assessment based on GOLD criteria`);

  } catch (error) {
    console.error('❌ Error updating data:', error);
    throw error;
  }
}

// Run the update function
updateRespiratoryToSpirometry()
  .then(() => {
    console.log('\n✅ Migration script completed successfully');
    console.log('🔄 Next steps:');
    console.log('   1. Update frontend components to use spirometry data');
    console.log('   2. Update charts to display spirometry metrics');
    console.log('   3. Update PDF reports to include spirometry data');
    console.log('   4. Test the updated dashboard and reports');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Migration script failed:', error);
    process.exit(1);
  });