const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

/**
 * <PERSON><PERSON>t to update existing respiratory rate data to include spirometry test parameters
 * This will:
 * 1. Update all patient readings to include spirometry data
 * 2. Update daily metrics to include spirometry metrics
 * 3. Update patient profile metrics to include spirometry data
 */
async function updateRespiratoryToSpirometry() {
  try {
    console.log('🔄 Starting respiratory data update to spirometry format...');
    
    // Get all patients
    const patientsSnapshot = await db.collection('patients').get();
    
    for (const patientDoc of patientsSnapshot.docs) {
      const patientId = patientDoc.id;
      console.log(`Processing patient: ${patientId}`);
      
      // Update readings
      const readingsSnapshot = await db.collection(`patients/${patientId}/readings`).get();
      
      for (const readingDoc of readingsSnapshot.docs) {
        const reading = readingDoc.data();
        
        // Only update if it has respiratory rate but no spirometry data
        if (reading.respiratoryRate && !reading.spirometry) {
          // Convert respiratory rate to spirometry data
          // FEV1 is roughly respiratory rate / 10 (simplified conversion for demo)
          const fev1 = reading.respiratoryRate / 10;
          
          // Create spirometry object with calculated values
          const spirometry = {
            FEV1: fev1,
            FVC: fev1 * (1 + Math.random() * 0.5), // FVC is typically larger than FEV1
            FEV1_FVC: 0.7 + (Math.random() * 0.2), // Ratio between 0.7-0.9
            PEF: fev1 * (4 + Math.random() * 2),   // Peak expiratory flow
            FEF25_75: fev1 * (2 + Math.random() * 1)
          };
          
          // Update the reading document
          await readingDoc.ref.update({
            spirometry,
            // Keep original respiratory rate for backward compatibility
            respiratoryRate: reading.respiratoryRate
          });
          
          console.log(`Updated reading ${readingDoc.id} for patient ${patientId}`);
        }
      }
      
      // Update daily metrics
      const dailyMetricsSnapshot = await db.collection(`patients/${patientId}/daily_metrics`).get();
      
      for (const metricDoc of dailyMetricsSnapshot.docs) {
        const metric = metricDoc.data();
        
        if (metric.respiratoryRate && !metric.spirometry) {
          const fev1 = metric.respiratoryRate / 10;
          
          await metricDoc.ref.update({
            spirometry: {
              FEV1: fev1,
              FVC: fev1 * (1 + Math.random() * 0.5),
              FEV1_FVC: 0.7 + (Math.random() * 0.2),
              PEF: fev1 * (4 + Math.random() * 2),
              FEF25_75: fev1 * (2 + Math.random() * 1)
            }
          });
          
          console.log(`Updated daily metric ${metricDoc.id} for patient ${patientId}`);
        }
      }
      
      // Update patient profile metrics
      const profileMetricsRef = db.doc(`patients/${patientId}/profile/metrics`);
      const profileMetrics = await profileMetricsRef.get();
      
      if (profileMetrics.exists) {
        const metrics = profileMetrics.data();
        
        if (metrics.latestReading && metrics.latestReading.respiratoryRate && !metrics.latestReading.spirometry) {
          const fev1 = metrics.latestReading.respiratoryRate / 10;
          
          await profileMetricsRef.update({
            'latestReading.spirometry': {
              FEV1: fev1,
              FVC: fev1 * (1 + Math.random() * 0.5),
              FEV1_FVC: 0.7 + (Math.random() * 0.2),
              PEF: fev1 * (4 + Math.random() * 2),
              FEF25_75: fev1 * (2 + Math.random() * 1)
            }
          });
          
          console.log(`Updated profile metrics for patient ${patientId}`);
        }
      }
      
      // Update health records
      const healthRecordsRef = db.doc(`patients/${patientId}/health_records/readings`);
      const healthRecords = await healthRecordsRef.get();
      
      if (healthRecords.exists) {
        const records = healthRecords.data();
        let updated = false;
        
        // Update latest reading
        if (records.latest && records.latest.respiratoryRate && !records.latest.spirometry) {
          const fev1 = records.latest.respiratoryRate / 10;
          records.latest.spirometry = {
            FEV1: fev1,
            FVC: fev1 * (1 + Math.random() * 0.5),
            FEV1_FVC: 0.7 + (Math.random() * 0.2),
            PEF: fev1 * (4 + Math.random() * 2),
            FEF25_75: fev1 * (2 + Math.random() * 1)
          };
          updated = true;
        }
        
        // Update recent readings
        if (records.recent && Array.isArray(records.recent)) {
          records.recent.forEach(reading => {
            if (reading.respiratoryRate && !reading.spirometry) {
              const fev1 = reading.respiratoryRate / 10;
              reading.spirometry = {
                FEV1: fev1,
                FVC: fev1 * (1 + Math.random() * 0.5),
                FEV1_FVC: 0.7 + (Math.random() * 0.2),
                PEF: fev1 * (4 + Math.random() * 2),
                FEF25_75: fev1 * (2 + Math.random() * 1)
              };
              updated = true;
            }
          });
        }
        
        if (updated) {
          await healthRecordsRef.set(records);
          console.log(`Updated health records for patient ${patientId}`);
        }
      }
    }
    
    console.log('✅ Successfully updated respiratory data to include spirometry parameters');
  } catch (error) {
    console.error('❌ Error updating data:', error);
  }
}

// Run the update function
updateRespiratoryToSpirometry()
  .then(() => {
    console.log('Update completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Update failed:', error);
    process.exit(1);
  });